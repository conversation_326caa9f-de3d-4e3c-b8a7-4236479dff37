#!/usr/bin/env python3
"""
ECS Cluster Deletion Script
Safely deletes ECS clusters and their associated resources.
"""

import boto3
import sys
import time
from botocore.exceptions import ClientError, NoCredentialsError

# Configuration
DRY_RUN = True  # Set to False to perform actual deletion
CLUSTER_NAMES = []  # Leave empty to delete ALL clusters, or specify: ['cluster1', 'cluster2']

# Initialize boto3 session
try:
    session = boto3.Session()
    print(f"[INFO] Using AWS profile: {session.profile_name or 'default'}")
except Exception as e:
    print(f"[ERROR] Failed to initialize AWS session: {e}")
    sys.exit(1)

def get_regions():
    """Get all enabled AWS regions."""
    try:
        ec2_client = session.client('ec2', region_name='us-east-1')
        regions = ec2_client.describe_regions()['Regions']
        return [r['RegionName'] for r in regions if r['OptInStatus'] in ['opt-in-not-required', 'opted-in']]
    except Exception as e:
        print(f"[ERROR] Failed to get regions: {e}")
        return ['us-east-1']  # Fallback to us-east-1

def get_ecs_client(region):
    """Get ECS client for specified region."""
    try:
        return session.client('ecs', region_name=region)
    except Exception as e:
        print(f"[ERROR] Failed to create ECS client for {region}: {e}")
        return None

def list_clusters(ecs_client, region):
    """List all ECS clusters in a region."""
    try:
        response = ecs_client.list_clusters()
        cluster_arns = response.get('clusterArns', [])
        
        if not cluster_arns:
            return []
        
        # Get cluster details
        cluster_details = ecs_client.describe_clusters(clusters=cluster_arns)
        clusters = []
        
        for cluster in cluster_details['clusters']:
            clusters.append({
                'name': cluster['clusterName'],
                'arn': cluster['clusterArn'],
                'status': cluster['status'],
                'running_tasks': cluster['runningTasksCount'],
                'pending_tasks': cluster['pendingTasksCount'],
                'active_services': cluster['activeServicesCount']
            })
        
        return clusters
    except ClientError as e:
        print(f"[ERROR] Failed to list clusters in {region}: {e}")
        return []

def stop_services(ecs_client, cluster_arn, region):
    """Stop all services in a cluster."""
    try:
        # List services
        services_response = ecs_client.list_services(cluster=cluster_arn)
        service_arns = services_response.get('serviceArns', [])
        
        if not service_arns:
            print(f"[INFO] No services found in cluster")
            return True
        
        print(f"[INFO] Found {len(service_arns)} services to stop")
        
        for service_arn in service_arns:
            service_name = service_arn.split('/')[-1]
            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would stop service: {service_name}")
                else:
                    print(f"[DELETE] Stopping service: {service_name}")
                    # Scale service to 0 desired count
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_arn,
                        desiredCount=0
                    )
                    time.sleep(2)
            except ClientError as e:
                print(f"[ERROR] Failed to stop service {service_name}: {e}")
        
        if not DRY_RUN:
            print("[INFO] Waiting 30 seconds for services to stop...")
            time.sleep(30)
        
        return True
    except ClientError as e:
        print(f"[ERROR] Failed to stop services: {e}")
        return False

def stop_tasks(ecs_client, cluster_arn, region):
    """Stop all running tasks in a cluster."""
    try:
        # List running tasks
        tasks_response = ecs_client.list_tasks(cluster=cluster_arn, desiredStatus='RUNNING')
        task_arns = tasks_response.get('taskArns', [])
        
        if not task_arns:
            print(f"[INFO] No running tasks found")
            return True
        
        print(f"[INFO] Found {len(task_arns)} running tasks to stop")
        
        for task_arn in task_arns:
            task_id = task_arn.split('/')[-1]
            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would stop task: {task_id}")
                else:
                    print(f"[DELETE] Stopping task: {task_id}")
                    ecs_client.stop_task(cluster=cluster_arn, task=task_arn, reason='Cluster cleanup')
                    time.sleep(1)
            except ClientError as e:
                print(f"[ERROR] Failed to stop task {task_id}: {e}")
        
        if not DRY_RUN:
            print("[INFO] Waiting 20 seconds for tasks to stop...")
            time.sleep(20)
        
        return True
    except ClientError as e:
        print(f"[ERROR] Failed to stop tasks: {e}")
        return False

def delete_services(ecs_client, cluster_arn, region):
    """Delete all services in a cluster."""
    try:
        # List services
        services_response = ecs_client.list_services(cluster=cluster_arn)
        service_arns = services_response.get('serviceArns', [])
        
        if not service_arns:
            return True
        
        print(f"[INFO] Deleting {len(service_arns)} services")
        
        for service_arn in service_arns:
            service_name = service_arn.split('/')[-1]
            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would delete service: {service_name}")
                else:
                    print(f"[DELETE] Deleting service: {service_name}")
                    ecs_client.delete_service(cluster=cluster_arn, service=service_arn, force=True)
                    time.sleep(2)
            except ClientError as e:
                print(f"[ERROR] Failed to delete service {service_name}: {e}")
        
        return True
    except ClientError as e:
        print(f"[ERROR] Failed to delete services: {e}")
        return False

def delete_cluster(ecs_client, cluster_name, cluster_arn, region):
    """Delete an ECS cluster."""
    try:
        if DRY_RUN:
            print(f"[DRY RUN] Would delete cluster: {cluster_name}")
        else:
            print(f"[DELETE] Deleting cluster: {cluster_name}")
            ecs_client.delete_cluster(cluster=cluster_arn)
            print(f"[SUCCESS] Cluster {cluster_name} deleted")
        return True
    except ClientError as e:
        print(f"[ERROR] Failed to delete cluster {cluster_name}: {e}")
        return False

def cleanup_cluster(ecs_client, cluster, region):
    """Complete cleanup process for a single cluster."""
    cluster_name = cluster['name']
    cluster_arn = cluster['arn']
    
    print(f"\n--- Processing cluster: {cluster_name} ({region}) ---")
    print(f"Status: {cluster['status']}")
    print(f"Running tasks: {cluster['running_tasks']}")
    print(f"Pending tasks: {cluster['pending_tasks']}")
    print(f"Active services: {cluster['active_services']}")
    
    # Step 1: Stop all services (scale to 0)
    if cluster['active_services'] > 0:
        print(f"[STEP 1] Stopping services...")
        if not stop_services(ecs_client, cluster_arn, region):
            return False
    
    # Step 2: Stop all running tasks
    if cluster['running_tasks'] > 0 or cluster['pending_tasks'] > 0:
        print(f"[STEP 2] Stopping tasks...")
        if not stop_tasks(ecs_client, cluster_arn, region):
            return False
    
    # Step 3: Delete services
    if cluster['active_services'] > 0:
        print(f"[STEP 3] Deleting services...")
        if not delete_services(ecs_client, cluster_arn, region):
            return False
    
    # Step 4: Delete the cluster
    print(f"[STEP 4] Deleting cluster...")
    return delete_cluster(ecs_client, cluster_name, cluster_arn, region)

def main():
    """Main execution function."""
    print("="*80)
    print("ECS CLUSTER DELETION TOOL")
    print("="*80)
    print(f"DRY RUN MODE: {'ENABLED' if DRY_RUN else 'DISABLED'}")
    
    if CLUSTER_NAMES:
        print(f"Target clusters: {', '.join(CLUSTER_NAMES)}")
    else:
        print("Target: ALL ECS clusters")
    
    print("="*80)
    
    if not DRY_RUN:
        print("\n⚠️  WARNING: This will DELETE ECS clusters and all their resources!")
        print("⚠️  This includes services, tasks, and the clusters themselves!")
        response = input("\nType 'DELETE' to confirm you want to proceed: ")
        if response != 'DELETE':
            print("Deletion cancelled.")
            return
    
    regions = get_regions()
    print(f"\n[INFO] Checking {len(regions)} regions for ECS clusters...")
    
    total_clusters = 0
    deleted_clusters = 0
    
    for region in regions:
        print(f"\n=== Region: {region} ===")
        
        ecs_client = get_ecs_client(region)
        if not ecs_client:
            continue
        
        clusters = list_clusters(ecs_client, region)
        if not clusters:
            print(f"[INFO] No ECS clusters found in {region}")
            continue
        
        print(f"[INFO] Found {len(clusters)} clusters in {region}")
        
        for cluster in clusters:
            # Filter clusters if specific names provided
            if CLUSTER_NAMES and cluster['name'] not in CLUSTER_NAMES:
                print(f"[SKIP] Skipping cluster {cluster['name']} (not in target list)")
                continue
            
            total_clusters += 1
            
            if cleanup_cluster(ecs_client, cluster, region):
                deleted_clusters += 1
            else:
                print(f"[ERROR] Failed to cleanup cluster {cluster['name']}")
    
    print(f"\n{'='*80}")
    print(f"CLEANUP SUMMARY")
    print(f"{'='*80}")
    print(f"Total clusters found: {total_clusters}")
    if DRY_RUN:
        print(f"Clusters that would be deleted: {deleted_clusters}")
        print(f"\n[INFO] Set DRY_RUN=False to perform actual deletion")
    else:
        print(f"Clusters successfully deleted: {deleted_clusters}")
        print(f"[SUCCESS] ECS cluster cleanup completed!")

if __name__ == "__main__":
    main()
