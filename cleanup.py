#!/usr/bin/env python3
"""
AWS Service Discovery Tool (Enhanced)
Lists all active resources for all regions and key services including VPCs & ENIs
"""

import boto3
from botocore.exceptions import ClientError

session = boto3.Session()
clients = {}

def get_client(service, region=None):
    key = f"{service}_{region or 'global'}"
    if key not in clients:
        clients[key] = session.client(service, region_name=region) if region else session.client(service)
    return clients[key]

def list_regions():
    ec2 = get_client('ec2', 'us-east-1')
    regions = ec2.describe_regions(AllRegions=True)['Regions']
    return [r['RegionName'] for r in regions if r['OptInStatus'] in ['opt-in-not-required', 'opted-in']]

def list_ec2(region):
    ec2 = get_client('ec2', region)
    instances = ec2.describe_instances(Filters=[{'Name': 'instance-state-name', 'Values': ['running']}])
    return [i['InstanceId'] for r in instances['Reservations'] for i in r['Instances']]

def list_vpcs(region):
    ec2 = get_client('ec2', region)
    vpcs = ec2.describe_vpcs()['Vpcs']
    return [vpc['VpcId'] for vpc in vpcs]

def list_enis(region):
    ec2 = get_client('ec2', region)
    enis = ec2.describe_network_interfaces()['NetworkInterfaces']
    return [eni['NetworkInterfaceId'] for eni in enis]

def list_s3():
    s3 = get_client('s3')
    return [b['Name'] for b in s3.list_buckets()['Buckets']]

def list_lambda(region):
    lam = get_client('lambda', region)
    return [f['FunctionName'] for f in lam.list_functions()['Functions']]

def list_rds(region):
    rds = get_client('rds', region)
    return [db['DBInstanceIdentifier'] for db in rds.describe_db_instances()['DBInstances']]

def list_ecs(region):
    ecs = get_client('ecs', region)
    return ecs.list_clusters()['clusterArns']

def list_eks(region):
    eks = get_client('eks', region)
    return eks.list_clusters()['clusters']

def list_sagemaker(region):
    sm = get_client('sagemaker', region)
    notebooks = [n['NotebookInstanceName'] for n in sm.list_notebook_instances()['NotebookInstances']]
    endpoints = [e['EndpointName'] for e in sm.list_endpoints()['Endpoints']]
    return notebooks + endpoints

def list_cloudformation(region):
    cf = get_client('cloudformation', region)
    return [s['StackName'] for s in cf.describe_stacks()['Stacks'] if s['StackStatus'] not in ['DELETE_COMPLETE']]

def list_dynamodb(region):
    ddb = get_client('dynamodb', region)
    return ddb.list_tables()['TableNames']

def list_secrets(region):
    sm = get_client('secretsmanager', region)
    return [s['Name'] for s in sm.list_secrets()['SecretList']]

def list_cloudfront():
    cf = get_client('cloudfront')
    return [d['Id'] for d in cf.list_distributions()['DistributionList'].get('Items', [])]

def main():
    print("=== AWS ACTIVE RESOURCES REPORT ===\n")

    regions = list_regions()
    print(f"Regions Found: {', '.join(regions)}\n")

    # Global Services
    print(f"[GLOBAL] S3 Buckets: {list_s3()}")
    print(f"[GLOBAL] CloudFront Distributions: {list_cloudfront()}")

    # Regional Services
    for region in regions:
        print(f"\n--- Region: {region} ---")

        try:
            print(f"EC2 Instances: {list_ec2(region)}")
            print(f"VPCs: {list_vpcs(region)}")
            print(f"ENIs: {list_enis(region)}")
            print(f"Lambda Functions: {list_lambda(region)}")
            print(f"RDS Databases: {list_rds(region)}")
            print(f"ECS Clusters: {list_ecs(region)}")
            print(f"EKS Clusters: {list_eks(region)}")
            print(f"SageMaker Resources: {list_sagemaker(region)}")
            print(f"CloudFormation Stacks: {list_cloudformation(region)}")
            print(f"DynamoDB Tables: {list_dynamodb(region)}")
            print(f"Secrets Manager Secrets: {list_secrets(region)}")
        except ClientError as e:
            print(f"[ERROR] {e}")

if __name__ == "__main__":
    main()
