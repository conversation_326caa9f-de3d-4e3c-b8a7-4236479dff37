#!/usr/bin/env python3
"""
Enhanced AWS Security Incident Cleanup Tool
Comprehensive cleanup of resources created during security incident based on CloudTrail analysis.
"""

import csv
import json
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from datetime import datetime, timedelta
import time
import sys

# Configuration
CLOUDTRAIL_CSV = "cloudtrail.csv"
DRY_RUN = False                      # Set False for actual cleanup
DISABLE_REGIONS = False             # Set True to disable opt-in regions
INCIDENT_START_TIME = "2025-07-31T07:00:00Z"  # Adjust based on your incident
INCIDENT_END_TIME = "2025-07-31T09:00:00Z"    # Adjust based on your incident

# Suspicious users identified from analysis
MALICIOUS_USERS = {
    'a-8175',           # Primary malicious user
    'agent-ai',         # Potentially compromised
  
}

# Suspicious IPs identified from analysis
MALICIOUS_IPS = {
    '*************',    # DigitalOcean
    '**************',   # DigitalOcean
    '************',     # Unknown
    '**************',   # Philippines
    '***********',      # Suspicious AWS IP
}

# Initialize boto3 session and clients
try:
    session = boto3.Session()
    print(f"[INFO] Using AWS profile: {session.profile_name or 'default'}")
except Exception as e:
    print(f"[ERROR] Failed to initialize AWS session: {e}")
    sys.exit(1)

# Global client cache
clients = {}

def get_client(service, region='us-east-1'):
    """Get or create AWS service client for specified region."""
    key = f"{service}_{region}"
    if key not in clients:
        try:
            clients[key] = session.client(service, region_name=region)
        except Exception as e:
            print(f"[ERROR] Failed to create {service} client for {region}: {e}")
            return None
    return clients[key]

def parse_cloudtrail_for_cleanup():
    """Parse CloudTrail logs and identify resources for cleanup based on security criteria."""

    resources_to_cleanup = {
        "ec2_instances": set(),
        "ec2_volumes": set(),
        "network_interfaces": set(),
        "security_groups": set(),
        "vpcs": set(),
        "subnets": set(),
        "iam_users": set(),
        "iam_roles": set(),
        "iam_policies": set(),
        "iam_groups": set(),
        "s3_buckets": set(),
        "lambda_functions": set(),
        "rds_instances": set(),
        "other_resources": set()
    }

    malicious_resources = set()
    suspicious_resources = set()

    print(f"[INFO] Parsing CloudTrail log: {CLOUDTRAIL_CSV}")

    try:
        with open(CLOUDTRAIL_CSV, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)

            for row in reader:
                user_name = row.get('User name', '').strip()
                event_time = row.get('Event time', '')
                event_name = row.get('Event name', '')
                source_ip = row.get('Source IP address', '')
                resources_field = row.get('Resources', '')
                aws_region = row.get('AWS region', 'us-east-1')

                # Check if event is within incident timeframe
                is_incident_time = False
                try:
                    event_dt = datetime.fromisoformat(event_time.replace('Z', '+00:00'))
                    incident_start = datetime.fromisoformat(INCIDENT_START_TIME.replace('Z', '+00:00'))
                    incident_end = datetime.fromisoformat(INCIDENT_END_TIME.replace('Z', '+00:00'))
                    is_incident_time = incident_start <= event_dt <= incident_end
                except:
                    pass

                # Determine if resource should be cleaned up
                is_malicious = (user_name in MALICIOUS_USERS or
                              source_ip in MALICIOUS_IPS or
                              is_incident_time)

                # Parse resources from the event
                if resources_field.strip() and resources_field != '[]':
                    try:
                        resources = json.loads(resources_field)
                        for res in resources:
                            resource_name = res.get('resourceName', '')
                            resource_type = res.get('resourceType', '')

                            if not resource_name:
                                continue

                            resource_info = (resource_name, aws_region, user_name, event_time)

                            # Categorize resources by type for cleanup
                            if resource_name.startswith('i-'):
                                resources_to_cleanup["ec2_instances"].add(resource_info)
                            elif resource_name.startswith('vol-'):
                                resources_to_cleanup["ec2_volumes"].add(resource_info)
                            elif resource_name.startswith('eni-'):
                                resources_to_cleanup["network_interfaces"].add(resource_info)
                            elif resource_name.startswith('sg-'):
                                resources_to_cleanup["security_groups"].add(resource_info)
                            elif resource_name.startswith('vpc-'):
                                resources_to_cleanup["vpcs"].add(resource_info)
                            elif resource_name.startswith('subnet-'):
                                resources_to_cleanup["subnets"].add(resource_info)
                            elif 'IAM::User' in resource_type:
                                resources_to_cleanup["iam_users"].add(resource_info)
                            elif 'IAM::Role' in resource_type:
                                resources_to_cleanup["iam_roles"].add(resource_info)
                            elif 'IAM::Policy' in resource_type:
                                resources_to_cleanup["iam_policies"].add(resource_info)
                            elif 'IAM::Group' in resource_type:
                                resources_to_cleanup["iam_groups"].add(resource_info)
                            else:
                                resources_to_cleanup["other_resources"].add(resource_info)

                            # Track malicious vs suspicious
                            if is_malicious:
                                malicious_resources.add(resource_info)
                            else:
                                suspicious_resources.add(resource_info)

                    except json.JSONDecodeError:
                        continue

    except FileNotFoundError:
        print(f"[ERROR] CloudTrail file not found: {CLOUDTRAIL_CSV}")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Failed to parse CloudTrail logs: {e}")
        sys.exit(1)

    return resources_to_cleanup, malicious_resources, suspicious_resources

def cleanup_ec2_instances(instances):
    """Terminate EC2 instances."""
    print(f"\n[INFO] Cleaning up {len(instances)} EC2 instances...")

    for instance_id, region, user, event_time in instances:
        ec2_client = get_client('ec2', region)
        if not ec2_client:
            continue

        try:
            if DRY_RUN:
                print(f"[DRY RUN] Would terminate instance: {instance_id} in {region} (created by {user})")
            else:
                print(f"[DELETE] Terminating instance: {instance_id} in {region}")
                ec2_client.terminate_instances(InstanceIds=[instance_id])
                time.sleep(0.5)  # Rate limiting
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'InvalidInstanceID.NotFound':
                print(f"[SKIP] Instance {instance_id} already deleted")
            else:
                print(f"[ERROR] Failed to terminate {instance_id}: {e}")

def cleanup_ec2_volumes(volumes):
    """Delete EBS volumes."""
    print(f"\n[INFO] Cleaning up {len(volumes)} EBS volumes...")

    for volume_id, region, user, event_time in volumes:
        ec2_client = get_client('ec2', region)
        if not ec2_client:
            continue

        try:
            if DRY_RUN:
                print(f"[DRY RUN] Would delete volume: {volume_id} in {region} (created by {user})")
            else:
                print(f"[DELETE] Deleting volume: {volume_id} in {region}")
                ec2_client.delete_volume(VolumeId=volume_id)
                time.sleep(0.5)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code in ['InvalidVolume.NotFound', 'InvalidVolumeID.NotFound']:
                print(f"[SKIP] Volume {volume_id} already deleted")
            elif error_code == 'VolumeInUse':
                print(f"[SKIP] Volume {volume_id} still in use, will retry later")
            else:
                print(f"[ERROR] Failed to delete volume {volume_id}: {e}")

def cleanup_network_interfaces(enis):
    """Delete network interfaces."""
    print(f"\n[INFO] Cleaning up {len(enis)} network interfaces...")

    for eni_id, region, user, event_time in enis:
        ec2_client = get_client('ec2', region)
        if not ec2_client:
            continue

        try:
            if DRY_RUN:
                print(f"[DRY RUN] Would delete ENI: {eni_id} in {region} (created by {user})")
            else:
                print(f"[DELETE] Deleting ENI: {eni_id} in {region}")
                ec2_client.delete_network_interface(NetworkInterfaceId=eni_id)
                time.sleep(0.5)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'InvalidNetworkInterfaceID.NotFound':
                print(f"[SKIP] ENI {eni_id} already deleted")
            elif error_code == 'InvalidNetworkInterface.InUse':
                print(f"[SKIP] ENI {eni_id} still in use")
            else:
                print(f"[ERROR] Failed to delete ENI {eni_id}: {e}")

def cleanup_security_groups(security_groups):
    """Delete security groups."""
    print(f"\n[INFO] Cleaning up {len(security_groups)} security groups...")

    for sg_id, region, user, event_time in security_groups:
        if sg_id == 'default':  # Skip default security groups
            continue

        ec2_client = get_client('ec2', region)
        if not ec2_client:
            continue

        try:
            if DRY_RUN:
                print(f"[DRY RUN] Would delete security group: {sg_id} in {region} (created by {user})")
            else:
                print(f"[DELETE] Deleting security group: {sg_id} in {region}")
                ec2_client.delete_security_group(GroupId=sg_id)
                time.sleep(0.5)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'InvalidGroupId.NotFound':
                print(f"[SKIP] Security group {sg_id} already deleted")
            elif error_code == 'DependencyViolation':
                print(f"[SKIP] Security group {sg_id} has dependencies, will retry later")
            else:
                print(f"[ERROR] Failed to delete security group {sg_id}: {e}")

def cleanup_iam_resources(users, roles, policies, groups):
    """Clean up IAM resources created by malicious users."""
    iam_client = get_client('iam')
    if not iam_client:
        return

    # Clean up IAM users
    if users:
        print(f"\n[INFO] Cleaning up {len(users)} IAM users...")
        for user_name, region, creator, event_time in users:
            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would delete IAM user: {user_name} (created by {creator})")
                else:
                    # Detach policies first
                    try:
                        attached_policies = iam_client.list_attached_user_policies(UserName=user_name)
                        for policy in attached_policies['AttachedPolicies']:
                            iam_client.detach_user_policy(UserName=user_name, PolicyArn=policy['PolicyArn'])
                    except:
                        pass

                    # Remove from groups
                    try:
                        groups_for_user = iam_client.get_groups_for_user(UserName=user_name)
                        for group in groups_for_user['Groups']:
                            iam_client.remove_user_from_group(GroupName=group['GroupName'], UserName=user_name)
                    except:
                        pass

                    # Delete access keys
                    try:
                        access_keys = iam_client.list_access_keys(UserName=user_name)
                        for key in access_keys['AccessKeyMetadata']:
                            iam_client.delete_access_key(UserName=user_name, AccessKeyId=key['AccessKeyId'])
                    except:
                        pass

                    print(f"[DELETE] Deleting IAM user: {user_name}")
                    iam_client.delete_user(UserName=user_name)
                    time.sleep(1)
            except ClientError as e:
                if e.response['Error']['Code'] == 'NoSuchEntity':
                    print(f"[SKIP] IAM user {user_name} already deleted")
                else:
                    print(f"[ERROR] Failed to delete IAM user {user_name}: {e}")

    # Clean up IAM roles
    if roles:
        print(f"\n[INFO] Cleaning up {len(roles)} IAM roles...")
        for role_name, region, creator, event_time in roles:
            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would delete IAM role: {role_name} (created by {creator})")
                else:
                    # Detach policies first
                    try:
                        attached_policies = iam_client.list_attached_role_policies(RoleName=role_name)
                        for policy in attached_policies['AttachedPolicies']:
                            iam_client.detach_role_policy(RoleName=role_name, PolicyArn=policy['PolicyArn'])
                    except:
                        pass

                    print(f"[DELETE] Deleting IAM role: {role_name}")
                    iam_client.delete_role(RoleName=role_name)
                    time.sleep(1)
            except ClientError as e:
                if e.response['Error']['Code'] == 'NoSuchEntity':
                    print(f"[SKIP] IAM role {role_name} already deleted")
                else:
                    print(f"[ERROR] Failed to delete IAM role {role_name}: {e}")

def cleanup_vpcs_and_subnets(vpcs, subnets):
    """Clean up VPCs and subnets (order matters - subnets first)."""

    # Clean up subnets first
    if subnets:
        print(f"\n[INFO] Cleaning up {len(subnets)} subnets...")
        for subnet_id, region, user, event_time in subnets:
            ec2_client = get_client('ec2', region)
            if not ec2_client:
                continue

            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would delete subnet: {subnet_id} in {region} (created by {user})")
                else:
                    print(f"[DELETE] Deleting subnet: {subnet_id} in {region}")
                    ec2_client.delete_subnet(SubnetId=subnet_id)
                    time.sleep(0.5)
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == 'InvalidSubnetID.NotFound':
                    print(f"[SKIP] Subnet {subnet_id} already deleted")
                elif error_code == 'DependencyViolation':
                    print(f"[SKIP] Subnet {subnet_id} has dependencies")
                else:
                    print(f"[ERROR] Failed to delete subnet {subnet_id}: {e}")

    # Clean up VPCs after subnets
    if vpcs:
        print(f"\n[INFO] Cleaning up {len(vpcs)} VPCs...")
        for vpc_id, region, user, event_time in vpcs:
            ec2_client = get_client('ec2', region)
            if not ec2_client:
                continue

            try:
                if DRY_RUN:
                    print(f"[DRY RUN] Would delete VPC: {vpc_id} in {region} (created by {user})")
                else:
                    print(f"[DELETE] Deleting VPC: {vpc_id} in {region}")
                    ec2_client.delete_vpc(VpcId=vpc_id)
                    time.sleep(0.5)
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code == 'InvalidVpcID.NotFound':
                    print(f"[SKIP] VPC {vpc_id} already deleted")
                elif error_code == 'DependencyViolation':
                    print(f"[SKIP] VPC {vpc_id} has dependencies")
                else:
                    print(f"[ERROR] Failed to delete VPC {vpc_id}: {e}")

def main():
    """Main cleanup orchestration function."""
    print("="*80)
    print("AWS SECURITY INCIDENT CLEANUP TOOL")
    print("="*80)
    print(f"DRY RUN MODE: {'ENABLED' if DRY_RUN else 'DISABLED'}")
    print(f"Incident timeframe: {INCIDENT_START_TIME} to {INCIDENT_END_TIME}")
    print(f"Malicious users: {', '.join(MALICIOUS_USERS)}")
    print(f"Malicious IPs: {', '.join(MALICIOUS_IPS)}")
    print("="*80)

    if not DRY_RUN:
        print("\n⚠️  WARNING: This will DELETE AWS resources permanently!")
        print("⚠️  Make sure you have backups and have reviewed the resource list!")
        response = input("\nType 'DELETE' to confirm you want to proceed: ")
        if response != 'DELETE':
            print("Cleanup cancelled.")
            return

    # Parse CloudTrail logs
    resources, malicious, suspicious = parse_cloudtrail_for_cleanup()

    # Display summary (excluding EC2 resources)
    print(f"\n=== CLEANUP SUMMARY ===")
    ec2_categories = {"ec2_instances", "ec2_volumes", "network_interfaces", "security_groups"}
    non_ec2_resources = {k: v for k, v in resources.items() if k not in ec2_categories}
    total_resources = sum(len(resource_set) for resource_set in non_ec2_resources.values())

    print(f"Total resources to process: {total_resources} (EC2 resources excluded)")
    print(f"Confirmed malicious resources: {len(malicious)}")
    print(f"Suspicious resources: {len(suspicious)}")

    for category, resource_set in resources.items():
        if resource_set:
            status = " (SKIPPED - EC2)" if category in ec2_categories else ""
            print(f"  {category.replace('_', ' ').title()}: {len(resource_set)}{status}")

    if DRY_RUN:
        print(f"\n[DRY RUN] Would clean up {total_resources} resources")

        # Show sample resources for each category
        for category, resource_set in resources.items():
            if resource_set:
                print(f"\n-- {category.upper().replace('_', ' ')} --")
                for i, (name, region, user, event_time) in enumerate(list(resource_set)[:5]):
                    print(f"  {name} ({region}) created by {user} at {event_time}")
                if len(resource_set) > 5:
                    print(f"  ... and {len(resource_set) - 5} more")

        print(f"\n[INFO] Set DRY_RUN=False to perform actual cleanup")
        return

    # Perform actual cleanup in proper order
    print(f"\n[INFO] Starting cleanup of {total_resources} resources...")
    print(f"[INFO] Skipping EC2 resources (instances, volumes, network interfaces, security groups)")

    # Skip EC2 cleanup as requested
    # 1. EC2 instances - SKIPPED
    # 2. Network interfaces - SKIPPED
    # 3. EBS volumes - SKIPPED
    # 4. Security groups - SKIPPED

    # 5. Clean up subnets and VPCs (in proper order)
    if resources["subnets"] or resources["vpcs"]:
        cleanup_vpcs_and_subnets(resources["vpcs"], resources["subnets"])

    # 6. Clean up IAM resources
    if any([resources["iam_users"], resources["iam_roles"], resources["iam_policies"], resources["iam_groups"]]):
        cleanup_iam_resources(
            resources["iam_users"],
            resources["iam_roles"],
            resources["iam_policies"],
            resources["iam_groups"]
        )

    # 7. Disable opt-in regions if requested
    if DISABLE_REGIONS:
        print("\n[INFO] Disabling opt-in AWS regions...")
        account_client = get_client('account')
        if account_client:
            try:
                regions = account_client.list_regions(
                    RegionOptStatusContains=['ENABLED', 'ENABLED_BY_DEFAULT']
                )
                for r in regions['Regions']:
                    if r['OptStatus'] == 'ENABLED':
                        print(f"[ACTION] Disabling region: {r['RegionName']}")
                        account_client.disable_region(RegionName=r['RegionName'])
            except ClientError as e:
                print(f"[ERROR] Failed to disable regions: {e}")

    print(f"\n[SUCCESS] Cleanup completed!")
    print(f"[INFO] Review AWS console to verify all malicious resources are removed")
    print(f"[INFO] Consider rotating all AWS access keys and reviewing IAM policies")

if __name__ == "__main__":
    main()
