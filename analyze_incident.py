#!/usr/bin/env python3
"""
CloudTrail Security Incident Analysis Tool
"""

import csv
import json
from collections import defaultdict
from datetime import datetime

def analyze_cloudtrail_logs():
    """Analyze CloudTrail logs for security incident patterns."""

    events_by_user = defaultdict(int)
    resource_types = defaultdict(int)
    event_names = defaultdict(int)
    suspicious_ips = defaultdict(int)
    resources_created = []
    time_analysis = defaultdict(int)

    print('Analyzing CloudTrail logs...')

    with open('cloudtrail.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)

        for i, row in enumerate(reader):
            if i % 500 == 0:
                print(f'Processed {i} rows...')

            user = row.get('User name', '').strip()
            event_name = row.get('Event name', '')
            source_ip = row.get('Source IP address', '')
            resources_field = row.get('Resources', '')
            event_time = row.get('Event time', '')
            aws_region = row.get('AWS region', '')

            events_by_user[user] += 1
            event_names[event_name] += 1

            # Track time patterns
            try:
                dt = datetime.fromisoformat(event_time.replace('Z', '+00:00'))
                hour_key = dt.strftime('%Y-%m-%d %H:00')
                time_analysis[hour_key] += 1
            except:
                pass

            # Track suspicious IPs
            if source_ip and not source_ip.endswith('.amazonaws.com') and source_ip != '':
                suspicious_ips[source_ip] += 1

            # Parse resources
            if resources_field.strip() and resources_field != '[]':
                try:
                    resources = json.loads(resources_field)
                    for res in resources:
                        resource_name = res.get('resourceName', '')
                        resource_type = res.get('resourceType', '')

                        if resource_type:
                            resource_types[resource_type] += 1

                        # Track resource creations for cleanup
                        if resource_name and any(create_word in event_name.lower()
                                               for create_word in ['create', 'run', 'launch', 'put']):
                            resources_created.append({
                                'name': resource_name,
                                'type': resource_type,
                                'user': user,
                                'event': event_name,
                                'time': event_time,
                                'region': aws_region,
                                'ip': source_ip
                            })
                except json.JSONDecodeError:
                    pass

    print(f'\nProcessed {i+1} total events')

    # Print analysis results
    print('\n=== TOP USERS BY ACTIVITY ===')
    for user, count in sorted(events_by_user.items(), key=lambda x: x[1], reverse=True)[:10]:
        user_display = user if user else '[EMPTY]'
        print(f'{user_display}: {count} events')

    print('\n=== TOP EVENT TYPES ===')
    for event, count in sorted(event_names.items(), key=lambda x: x[1], reverse=True)[:15]:
        event_display = event if event else '[EMPTY]'
        print(f'{event_display}: {count} times')

    print('\n=== RESOURCE TYPES AFFECTED ===')
    for rtype, count in sorted(resource_types.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f'{rtype}: {count} resources')

    print('\n=== SUSPICIOUS IP ADDRESSES ===')
    for ip, count in sorted(suspicious_ips.items(), key=lambda x: x[1], reverse=True)[:10]:
        if count > 5:
            print(f'{ip}: {count} events')

    print('\n=== TIME-BASED ACTIVITY PEAKS ===')
    for time_period, count in sorted(time_analysis.items(), key=lambda x: x[1], reverse=True)[:10]:
        if count > 100:
            print(f'{time_period}: {count} events')

    print(f'\n=== RESOURCES CREATED (for cleanup) ===')
    print(f'Total resources created: {len(resources_created)}')
    if resources_created:
        print('Sample resources:')
        for res in resources_created[:10]:
            print(f'  {res["name"]} ({res["type"]}) by {res["user"]} at {res["time"]}')

    return resources_created

if __name__ == "__main__":
    resources = analyze_cloudtrail_logs()